<template>
  <div class="test-container">
    <h2>Enhanced Debug Report Features Test</h2>
    
    <!-- Status Display -->
    <div class="status-section">
      <h3>Status</h3>
      <div class="status-grid">
        <div class="status-item">
          <strong>Connection:</strong> 
          <span :class="connectionStore.isConnected ? 'online' : 'offline'">
            {{ connectionStore.isConnected ? 'Online' : 'Offline' }}
          </span>
        </div>
        <div class="status-item">
          <strong>Offline Reports:</strong> {{ offlineReports.offlineReportCount }}
        </div>
        <div class="status-item">
          <strong>Form Drafts:</strong> {{ formDrafts.draftCount }}
        </div>
        <div class="status-item">
          <strong>Processing Queue:</strong> {{ offlineReports.isProcessingQueue ? 'Yes' : 'No' }}
        </div>
      </div>
    </div>

    <!-- Test Actions -->
    <div class="actions-section">
      <h3>Test Actions</h3>
      <div class="button-grid">
        <n-button @click="testOfflineReport" type="primary">
          Add Test Offline Report
        </n-button>
        <n-button @click="testFormDraft" type="info">
          Save Test Form Draft
        </n-button>
        <n-button @click="processOfflineReports" :disabled="!offlineReports.hasOfflineReports">
          Process Offline Reports
        </n-button>
        <n-button @click="clearAllData" type="error">
          Clear All Test Data
        </n-button>
      </div>
    </div>

    <!-- Enhanced Debug Report Button -->
    <div class="debug-button-section">
      <h3>Enhanced Debug Report Button</h3>
      <DebugReportButtonEnhanced />
    </div>

    <!-- Statistics -->
    <div class="stats-section">
      <h3>Statistics</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <h4>Offline Reports</h4>
          <pre>{{ JSON.stringify(offlineReports.getOfflineReportStats(), null, 2) }}</pre>
        </div>
        <div class="stat-card">
          <h4>Form Drafts</h4>
          <pre>{{ JSON.stringify(formDrafts.getDraftStats(), null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import { useConnectionStore } from '@/stores/connection';
import { useClientLogger } from '@/composables/useClientLogger';
import { useMessage } from 'naive-ui';
import DebugReportButtonEnhanced from '@/components/DebugReportButtonEnhanced.vue';
import type { ClientReportPayload, ReportDetails } from '@/types/logging';

const offlineReports = useOfflineReports();
const formDrafts = useFormDrafts();
const connectionStore = useConnectionStore();
const logger = useClientLogger();
const message = useMessage();

// Test offline report functionality
const testOfflineReport = () => {
  const testReportPayload: ClientReportPayload = {
    logs: [],
    reportDetails: {
      type: 'bug',
      severity: 'high',
      title: 'Test offline bug report',
      description: 'This is a test bug report to verify offline functionality',
      stepsToReproduce: '1. Click test button\n2. Report is stored offline\n3. Will be submitted when online',
      expectedBehavior: 'Report should be submitted when online',
      actualBehavior: 'Report is stored offline for later submission',
      additionalNotes: 'This is a test report for offline functionality'
    },
    timestamp: new Date().toISOString(),
    sessionId: 'test-session-' + Date.now()
  };

  const reportId = offlineReports.addOfflineReport(testReportPayload);
  message.success(`Test offline report added with ID: ${reportId}`);
};

// Test form draft functionality
const testFormDraft = () => {
  const testFormData: ReportDetails = {
    type: 'feature-request',
    severity: 'medium',
    title: 'Test form draft feature',
    description: 'This is a test form draft to verify auto-save functionality',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    additionalNotes: 'This draft should be saved and restorable'
  };

  const draftId = formDrafts.saveDraft(testFormData);
  message.success(`Test form draft saved with ID: ${draftId}`);
};

// Process offline reports manually
const processOfflineReports = async () => {
  if (!offlineReports.hasOfflineReports) {
    message.warning('No offline reports to process');
    return;
  }

  try {
    await offlineReports.processOfflineReports();
    message.success('Offline reports processed successfully');
  } catch (error) {
    message.error('Failed to process offline reports');
    console.error('Error processing offline reports:', error);
  }
};

// Clear all test data
const clearAllData = () => {
  offlineReports.clearOfflineReports();
  formDrafts.clearAllDrafts();
  message.success('All test data cleared');
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container h2 {
  color: #333;
  margin-bottom: 20px;
}

.test-container h3 {
  color: #555;
  margin: 20px 0 10px 0;
}

.status-section,
.actions-section,
.debug-button-section,
.stats-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.status-item {
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.online {
  color: #18a058;
  font-weight: bold;
}

.offline {
  color: #d03050;
  font-weight: bold;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.stat-card h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.stat-card pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
}
</style>
