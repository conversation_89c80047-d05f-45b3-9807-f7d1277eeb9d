<template>
  <div class="debug-report-container">
    <n-button
      type="info"
      size="small"
      :loading="isLoading"
      :disabled="isLoading || logCount === 0"
      @click="handleSendReport"
      class="debug-report-button"
    >
      <template #icon>
        <n-icon>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
        </n-icon>
      </template>
      {{ $t('debugReport.sendReport') }} ({{ logCount }})
    </n-button>

    <!-- Enhanced Debug Report Modal -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      :title="$t('debugReport.modalTitle')"
      class="debug-report-modal"
      :mask-closable="false"
      style="width: 90vw; max-width: 800px;"
    >
      <div class="debug-report-content">
        <n-alert
          type="info"
          :show-icon="false"
          class="debug-info-alert"
        >
          {{ $t('debugReport.modalDescription') }}
        </n-alert>

        <n-form
          ref="formRef"
          :model="reportForm"
          :rules="formRules"
          label-placement="top"
          require-mark-placement="right-hanging"
          class="report-form"
        >          <!-- Report Type Selection -->
          <n-form-item :label="$t('debug.reportType')" path="type">            <n-select
              v-model:value="reportForm.type"
              :options="reportTypeOptions as any"
              :placeholder="$t('debug.reportType')"
              :render-label="renderTypeLabel"
              :render-option="renderTypeOption"
              class="report-type-select"
              @update:value="handleFormInput"
            />
          </n-form-item>

          <!-- Severity Selection -->
          <n-form-item :label="$t('debug.severityText')" path="severity">            <n-select
              v-model:value="reportForm.severity"
              :options="severityOptions as any"
              :placeholder="$t('debug.selectSeverity')"
              :render-label="renderSeverityLabel"
              :render-option="renderSeverityOption"
              class="severity-select"
              @update:value="handleFormInput"
            />
          </n-form-item>

          <!-- Auto-save notification -->
          <div v-if="formDrafts.hasDrafts || formDrafts.lastAutoSaveAt" class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800 mb-4">
            <div class="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              <span>ℹ️</span>
              <span class="text-sm">{{ $t('debug.autoSaveEnabled') }}</span>
            </div>
            <div v-if="formDrafts.lastAutoSaveAt" class="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Last saved: {{ formDrafts.lastAutoSaveAt ? formatTime(formDrafts.lastAutoSaveAt) : '' }}
            </div>
          </div>

          <!-- Title -->
          <n-form-item :label="$t('debug.title')" path="title">
            <n-input
              v-model:value="reportForm.title"
              :placeholder="$t('debug.titlePlaceholder')"
              :maxlength="200"
              show-count
              class="title-input"
              @input="handleFormInput"
            />
          </n-form-item>          <!-- Description -->
          <n-form-item :label="$t('debug.descriptionText')" path="description">
            <n-input
              v-model:value="reportForm.description"
              type="textarea"
              :placeholder="$t('debug.descriptionPlaceholder')"
              :rows="4"
              :maxlength="2000"
              show-count
              class="description-input"
              @input="handleFormInput"
            />
          </n-form-item>

          <!-- Steps to Reproduce (for bugs) -->
          <n-form-item
            v-if="reportForm.type === 'bug'"
            :label="$t('debug.stepsToReproduce')"
            path="stepsToReproduce"
          >
            <n-input
              v-model:value="reportForm.stepsToReproduce"
              type="textarea"
              :placeholder="$t('debug.stepsPlaceholder')"
              :rows="3"
              :maxlength="2000"
              show-count
              class="steps-input"
              @input="handleFormInput"
            />
          </n-form-item>

          <!-- Expected vs Actual Behavior (for bugs) -->
          <div v-if="reportForm.type === 'bug'" class="behavior-section">            <n-form-item :label="$t('debug.expectedBehavior')" path="expectedBehavior">
              <n-input
                v-model:value="reportForm.expectedBehavior"
                type="textarea"
                :placeholder="$t('debug.expectedPlaceholder')"
                :rows="2"
                :maxlength="1000"
                show-count
                class="expected-input"
                @input="handleFormInput"
              />
            </n-form-item>

            <n-form-item :label="$t('debug.actualBehavior')" path="actualBehavior">
              <n-input
                v-model:value="reportForm.actualBehavior"
                type="textarea"
                :placeholder="$t('debug.actualPlaceholder')"
                :rows="2"
                :maxlength="1000"
                show-count
                class="actual-input"
                @input="handleFormInput"
              />
            </n-form-item>
          </div>          <!-- Additional Notes -->
          <n-form-item :label="$t('debug.additionalNotes')" path="additionalNotes">
            <n-input
              v-model:value="reportForm.additionalNotes"
              type="textarea"
              :placeholder="$t('debug.additionalNotesPlaceholder')"
              :rows="3"
              :maxlength="1000"
              show-count
              class="additional-notes-input"
              @input="handleFormInput"
            />
          </n-form-item>
        </n-form>

        <n-divider />

        <!-- Technical Information -->
        <n-collapse class="technical-info-collapse">          <n-collapse-item :title="$t('debug.contextInfo')" name="technical">
            <div class="debug-stats">
              <n-descriptions :bordered="false" size="small" :column="2">
                <n-descriptions-item :label="$t('debugReport.totalLogs')">
                  {{ logCount }}
                </n-descriptions-item>
                <n-descriptions-item :label="$t('debug.currentPage')">
                  {{ currentUrl }}
                </n-descriptions-item>
                <n-descriptions-item :label="$t('debugReport.sessionId')">
                  {{ sessionId }}
                </n-descriptions-item>
                <n-descriptions-item :label="$t('debugReport.browser')">
                  {{ browserInfo }}
                </n-descriptions-item>
              </n-descriptions>
            </div>

            <div class="log-levels-summary" v-if="logLevelCounts">
              <h4>{{ $t('debugReport.logLevels') }}</h4>
              <div class="log-level-tags">
                <n-tag
                  v-for="(count, level) in logLevelCounts"
                  :key="level"
                  :type="getTagType(level)"
                  size="small"
                >
                  {{ level }}: {{ count }}
                </n-tag>
              </div>
            </div>
          </n-collapse-item>
        </n-collapse>
      </div>

      <template #action>
        <div class="modal-actions">
          <div class="flex gap-2">
            <n-button @click="handleCancel" :disabled="isLoading">
              {{ $t('common.cancel') }}
            </n-button>
            <n-button
              @click="handleResetForm"
              :disabled="isLoading"
              type="warning"
              ghost
            >
              {{ $t('debug.resetForm') }}
            </n-button>
          </div>
          <div class="flex gap-2 items-center">
            <!-- Offline indicator -->
            <div v-if="!connectionStore.isConnected" class="flex items-center gap-1 text-orange-500 text-sm">
              <span>⚠️</span>
              <span>{{ $t('debug.offlineMode') }}</span>
            </div>
            <!-- Pending offline reports indicator -->
            <div v-if="offlineReports.hasOfflineReports" class="flex items-center gap-1 text-blue-500 text-sm">
              <span>📤</span>
              <span>{{ $t('debug.offlineReportsCount', { count: offlineReports.offlineReportCount }) }}</span>
            </div>
            <n-button
              type="primary"
              :loading="isLoading"
              @click="handleSubmitReport"
            >
              {{ $t('debug.sendReport') }}
            </n-button>
          </div>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, watch } from 'vue';
import { useMessage, useDialog, type FormInst, type FormRules } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import { useClientLogger } from '@/composables/useClientLogger';
import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import { useConnectionStore } from '@/stores/connection';
import type { ReportTypeOption, ReportSeverityOption, ReportDetails } from '@/types/logging';

const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const logger = useClientLogger();
const offlineReports = useOfflineReports();
const formDrafts = useFormDrafts();
const connectionStore = useConnectionStore();

// Form ref for validation
const formRef = ref<FormInst | null>(null);

// Reactive state
const isLoading = ref(false);
const showModal = ref(false);
const currentUrl = ref('');
const sessionId = ref('');
const browserInfo = ref('');

// Form data
const reportForm = ref<ReportDetails>({
  type: 'bug',
  severity: 'medium',
  title: '',
  description: '',
  stepsToReproduce: '',
  expectedBehavior: '',
  actualBehavior: '',
  additionalNotes: '',
});

// Form validation rules
const formRules: FormRules = {
  type: {
    required: true,
    message: t('debug.formValidationError'),
    trigger: ['blur', 'change'],
  },
  severity: {
    required: true,
    message: t('debug.formValidationError'),
    trigger: ['blur', 'change'],
  },
  title: {
    required: true,
    message: t('debug.formValidationError'),
    trigger: ['blur', 'input'],
    min: 5,
    max: 200,
  },
  description: {
    required: true,
    message: t('debug.formValidationError'),
    trigger: ['blur', 'input'],
    min: 10,
    max: 2000,
  },
};

// Report type options
const reportTypeOptions: ReportTypeOption[] = [
  {
    type: 'option',
    value: 'bug',
    label: t('debug.reportTypes.bug'),
    description: t('debug.reportTypeDescriptions.bug'),
    icon: '🐛',
    color: '#d03050',
    tags: ['urgent', 'fix-needed'],
  },
  {
    type: 'option',
    value: 'feature-request',
    label: t('debug.reportTypes.feature'),
    description: t('debug.reportTypeDescriptions.feature'),
    icon: '💡',
    color: '#18a058',
    tags: ['enhancement', 'new-feature'],
  },
  {
    type: 'option',
    value: 'performance',
    label: t('debug.reportTypes.performance'),
    description: t('debug.reportTypeDescriptions.performance'),
    icon: '⚡',
    color: '#f0a020',
    tags: ['slow', 'optimization'],
  },
  {
    type: 'option',
    value: 'ui-ux',
    label: t('debug.reportTypes.uiux'),
    description: t('debug.reportTypeDescriptions.uiux'),
    icon: '🎨',
    color: '#722ed1',
    tags: ['design', 'user-experience'],
  },
  {
    type: 'option',
    value: 'improvement',
    label: t('debug.reportTypes.improvement'),
    description: t('debug.reportTypeDescriptions.improvement'),
    icon: '✨',
    color: '#1890ff',
    tags: ['better-way', 'suggestion'],
  },
  {
    type: 'option',
    value: 'question',
    label: t('debug.reportTypes.question'),
    description: t('debug.reportTypeDescriptions.question'),
    icon: '❓',
    color: '#13c2c2',
    tags: ['help', 'unclear'],
  },
  {
    type: 'option',
    value: 'other',
    label: t('debug.reportTypes.other'),
    description: t('debug.reportTypeDescriptions.other'),
    icon: '📝',
    color: '#8c8c8c',
    tags: ['miscellaneous', 'general'],
  },
] as any;

// Severity options
const severityOptions: ReportSeverityOption[] = [
  {
    type: 'option',
    value: 'low',
    label: t('debug.severity.low'),
    description: t('debug.severity.low'),
    color: '#18a058',
  },
  {
    type: 'option',
    value: 'medium',
    label: t('debug.severity.medium'),
    description: t('debug.severity.medium'),
    color: '#f0a020',
  },
  {
    type: 'option',
    value: 'high',
    label: t('debug.severity.high'),
    description: t('debug.severity.high'),
    color: '#d03050',
  },
  {
    type: 'option',
    value: 'critical',
    label: t('debug.severity.critical'),
    description: t('debug.severity.critical'),
    color: '#c41e3a',
  },
] as any;

// Computed properties
const logCount = computed(() => logger.getLogCount());

const logLevelCounts = computed(() => {
  const logs = logger.getLogs();
  return logs.reduce((acc, log) => {
    acc[log.level] = (acc[log.level] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
});

// Initialize component data
onMounted(() => {
  currentUrl.value = window.location.href;
  sessionId.value = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  browserInfo.value = getBrowserInfo();
});

// Methods
const getBrowserInfo = (): string => {
  const ua = navigator.userAgent;
  if (ua.includes('Chrome')) return 'Chrome';
  if (ua.includes('Firefox')) return 'Firefox';
  if (ua.includes('Safari')) return 'Safari';
  if (ua.includes('Edge')) return 'Edge';
  return 'Other';
};

const getTagType = (level: string) => {
  switch (level) {
    case 'ERROR': return 'error';
    case 'WARN': return 'warning';
    case 'INFO': return 'info';
    case 'DEBUG': return 'default';
    default: return 'default';
  }
};

// Render functions for select options
const renderTypeLabel = (option: ReportTypeOption) => {
  return h('span', {}, [
    h('span', { style: { marginRight: '8px' } }, option.icon),
    option.label
  ]);
};

const renderTypeOption = ({ option }: { option: ReportTypeOption }) => {
  return h('div', { style: { display: 'flex', alignItems: 'center', padding: '4px 0' } }, [
    h('span', { style: { marginRight: '8px', fontSize: '16px' } }, option.icon),
    h('div', {}, [
      h('div', { style: { fontWeight: '500' } }, option.label),
      h('div', { style: { fontSize: '12px', opacity: '0.7' } }, option.description)
    ])
  ]);
};

const renderSeverityLabel = (option: ReportSeverityOption) => {
  return h('span', { style: { color: option.color, fontWeight: '500' } }, option.label);
};

const renderSeverityOption = ({ option }: { option: ReportSeverityOption }) => {
  return h('div', { style: { display: 'flex', alignItems: 'center', padding: '4px 0' } }, [
    h('div', { 
      style: { 
        width: '12px', 
        height: '12px', 
        borderRadius: '50%', 
        backgroundColor: option.color, 
        marginRight: '8px' 
      } 
    }),
    h('div', {}, [
      h('div', { style: { fontWeight: '500', color: option.color } }, option.label),
      h('div', { style: { fontSize: '12px', opacity: '0.7' } }, option.description)
    ])
  ]);
};

const handleSendReport = () => {
  if (logCount.value === 0) {
    message.warning(t('debugReport.noLogsWarning'));
    return;
  }

  // Update current URL when modal opens
  currentUrl.value = window.location.href;

  // Reset form to defaults
  resetForm();

  showModal.value = true;

  // Check for existing drafts
  if (formDrafts.hasDrafts) {
    dialog.info({
      title: t('debug.restoreDraft'),
      content: t('debug.hasDrafts', { count: formDrafts.draftCount }),
      positiveText: t('debug.restoreDraft'),
      negativeText: t('debug.discardDraft'),
      onPositiveClick: () => {
        const latestDraft = formDrafts.getLatestDraft();
        if (latestDraft) {
          reportForm.value = { ...latestDraft };
          message.success(t('debug.draftLoaded'));
          logger.logUserAction('debug-draft-restored');
        }
      },
      onNegativeClick: () => {
        resetForm();
        logger.logUserAction('debug-draft-discarded');
      }
    });
  }
};

const handleCancel = () => {
  showModal.value = false;
  // Optionally reset form here if needed
};

const handleSubmitReport = async () => {
  if (!formRef.value) return;

  try {
    // Validate form first
    await formRef.value.validate();

    isLoading.value = true;

    logger.logInfo('User initiated structured debug report send', {
      reportType: reportForm.value.type,
      reportSeverity: reportForm.value.severity,
      logCount: logCount.value,
      hasStepsToReproduce: !!reportForm.value.stepsToReproduce,
      currentPage: currentUrl.value,
      isOnline: connectionStore.isConnected
    });

    if (!connectionStore.isConnected) {
      // Store report offline
      const reportPayload = {
        logs: logger.getLogs(),
        reportDetails: reportForm.value,
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        diagnosticData: logger.captureDiagnosticData(),
        userIdentification: logger.captureUserIdentification()
      };

      offlineReports.addOfflineReport(reportPayload);
      message.info(t('debug.offlineReportStored'));

      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else {
      // Submit online
      const result = await logger.sendLogsToServer(reportForm.value);

      if (result.success) {
        message.success(result.message || t('debugReport.sendSuccess'));

        // Close modal and reset form
        showModal.value = false;
        resetForm();
        formDrafts.clearActiveDraft();
      } else {
        message.error(result.message || t('debugReport.sendError'));
      }
    }

  } catch (error: any) {
    // Form validation failed
    message.error(t('debug.formValidationError'));
  } finally {
    isLoading.value = false;
  }
};

// Helper methods
const resetForm = () => {
  reportForm.value = {
    type: 'bug',
    severity: 'medium',
    title: '',
    description: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    additionalNotes: '',
  };
};

// Handle reset form with confirmation
const handleResetForm = () => {
  dialog.warning({
    title: t('debug.resetForm'),
    content: t('debug.resetFormConfirm'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      resetForm();
      formDrafts.clearActiveDraft();
      message.success(t('debug.resetFormSuccess'));
      logger.logUserAction('debug-form-reset');
    }
  });
};

// Handle form input changes for auto-save
const handleFormInput = () => {
  formDrafts.autoSaveDraft(reportForm.value);
};

// Format time helper
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString();
};

// Watch for connection changes
watch(() => connectionStore.isConnected, (isConnected) => {
  if (isConnected && offlineReports.hasOfflineReports) {
    message.info(t('debug.connectionRestored'));
  }
});

// Watch for offline reports processing
watch(() => offlineReports.isProcessingQueue, (isProcessing) => {
  if (isProcessing) {
    message.loading(t('debug.processingOfflineReports'));
  }
});

// Expose methods for parent components if needed
defineExpose({
  openModal: () => { showModal.value = true; },
  getLogCount: () => logCount.value,
});
</script>

<style scoped>
.debug-report-container {
  display: inline-block;
}

.debug-report-button {
  font-size: 12px;
}

.debug-report-content {
  padding: 8px 0;
}

.debug-info-alert {
  margin-bottom: 16px;
}

.report-form {
  margin-bottom: 16px;
}

.report-type-select,
.severity-select,
.title-input,
.description-input,
.steps-input,
.expected-input,
.actual-input,
.additional-notes-input {
  width: 100%;
}

.behavior-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.technical-info-collapse {
  margin-bottom: 16px;
}

.debug-stats {
  margin-bottom: 16px;
}

.log-levels-summary {
  margin-bottom: 16px;
}

.log-levels-summary h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.log-level-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.modal-actions {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.debug-report-modal {
  max-width: 800px;
}

@media (max-width: 768px) {
  .debug-report-modal {
    width: 95vw;
    margin: 0 auto;
  }
  
  .behavior-section {
    grid-template-columns: 1fr;
  }
  
  .log-level-tags {
    justify-content: flex-start;
  }
}
</style>
