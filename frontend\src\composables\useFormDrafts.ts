import { ref, computed, watch, nextTick, readonly } from 'vue';
import type { ReportDetails, FormDraft, FormDraftStorage } from '@/types/logging';
import { useClientLogger } from '@/composables/useClientLogger';
import { useI18n } from 'vue-i18n';
import { useMessage } from 'naive-ui';

const STORAGE_KEY = 'munygo-form-drafts';
const MAX_DRAFTS = 10;
const AUTO_SAVE_DELAY_MS = 2000; // 2 seconds
const CLEANUP_INTERVAL_DAYS = 30;

// Global state for form drafts
const formDrafts = ref<FormDraft[]>([]);
const activeDraftId = ref<string | null>(null);
const autoSaveTimer = ref<number | null>(null);
const lastAutoSaveAt = ref<string | null>(null);

/**
 * Composable for managing form draft auto-saving and restoration
 */
export function useFormDrafts() {
  const { t } = useI18n();
  const message = useMessage();
  const logger = useClientLogger();

  // Computed properties
  const hasDrafts = computed(() => formDrafts.value.length > 0);
  const draftCount = computed(() => formDrafts.value.length);
  const activeDraft = computed(() => 
    activeDraftId.value 
      ? formDrafts.value.find(draft => draft.id === activeDraftId.value) 
      : null
  );

  /**
   * Load form drafts from localStorage
   */
  const loadFormDrafts = (): void => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const storage: FormDraftStorage = JSON.parse(stored);
        formDrafts.value = storage.drafts || [];
        activeDraftId.value = storage.activeDraftId || null;
        
        // Clean up old drafts
        cleanupOldDrafts();
        
        logger.logInfo('Form drafts loaded', {
          count: formDrafts.value.length,
          activeDraftId: activeDraftId.value,
          lastCleanup: storage.lastCleanup
        });
      }
    } catch (error) {
      console.error('Failed to load form drafts:', error);
      formDrafts.value = [];
      activeDraftId.value = null;
    }
  };

  /**
   * Save form drafts to localStorage
   */
  const saveFormDrafts = (): void => {
    try {
      const storage: FormDraftStorage = {
        drafts: formDrafts.value,
        activeDraftId: activeDraftId.value || undefined,
        lastCleanup: new Date().toISOString()
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(storage));
    } catch (error) {
      console.error('Failed to save form drafts:', error);
      logger.logError('Failed to save form drafts to localStorage', error);
    }
  };

  /**
   * Create a new draft or update existing one
   */
  const saveDraft = (formData: ReportDetails, draftId?: string): string => {
    const now = new Date().toISOString();
    
    if (draftId) {
      // Update existing draft
      const existingIndex = formDrafts.value.findIndex(draft => draft.id === draftId);
      if (existingIndex !== -1) {
        formDrafts.value[existingIndex] = {
          ...formDrafts.value[existingIndex],
          formData: { ...formData },
          lastModified: now
        };
      }
    } else {
      // Create new draft
      const newDraftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const newDraft: FormDraft = {
        id: newDraftId,
        formData: { ...formData },
        timestamp: now,
        lastModified: now
      };

      formDrafts.value.unshift(newDraft);
      
      // Limit the number of stored drafts
      if (formDrafts.value.length > MAX_DRAFTS) {
        formDrafts.value = formDrafts.value.slice(0, MAX_DRAFTS);
      }
      
      draftId = newDraftId;
    }
    
    saveFormDrafts();
    lastAutoSaveAt.value = now;
    
    logger.logInfo('Form draft saved', {
      draftId,
      totalDrafts: formDrafts.value.length,
      hasTitle: !!formData.title,
      hasDescription: !!formData.description
    });

    return draftId;
  };

  /**
   * Auto-save form data with debouncing
   */
  const autoSaveDraft = (formData: ReportDetails, draftId?: string): void => {
    // Clear existing timer
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value);
    }

    // Only auto-save if there's meaningful content
    const hasContent = formData.title.trim() || 
                      formData.description.trim() || 
                      formData.stepsToReproduce?.trim() ||
                      formData.expectedBehavior?.trim() ||
                      formData.actualBehavior?.trim() ||
                      formData.additionalNotes?.trim();

    if (!hasContent) {
      return;
    }

    // Set new timer
    autoSaveTimer.value = window.setTimeout(() => {
      const savedDraftId = saveDraft(formData, draftId || activeDraftId.value || undefined);
      
      // Set as active draft if not already set
      if (!activeDraftId.value) {
        activeDraftId.value = savedDraftId;
      }
    }, AUTO_SAVE_DELAY_MS);
  };

  /**
   * Load a specific draft
   */
  const loadDraft = (draftId: string): ReportDetails | null => {
    const draft = formDrafts.value.find(d => d.id === draftId);
    if (draft) {
      activeDraftId.value = draftId;
      saveFormDrafts();
      
      logger.logInfo('Form draft loaded', {
        draftId,
        timestamp: draft.timestamp,
        lastModified: draft.lastModified
      });
      
      return { ...draft.formData };
    }
    return null;
  };

  /**
   * Get the most recent draft
   */
  const getLatestDraft = (): ReportDetails | null => {
    if (formDrafts.value.length === 0) {
      return null;
    }
    
    const latestDraft = formDrafts.value[0]; // Already sorted by newest first
    return loadDraft(latestDraft.id);
  };

  /**
   * Delete a specific draft
   */
  const deleteDraft = (draftId: string): void => {
    const index = formDrafts.value.findIndex(draft => draft.id === draftId);
    if (index !== -1) {
      formDrafts.value.splice(index, 1);
      
      // Clear active draft if it was deleted
      if (activeDraftId.value === draftId) {
        activeDraftId.value = null;
      }
      
      saveFormDrafts();
      
      logger.logInfo('Form draft deleted', {
        draftId,
        remainingDrafts: formDrafts.value.length
      });
    }
  };

  /**
   * Clear all drafts
   */
  const clearAllDrafts = (): void => {
    formDrafts.value = [];
    activeDraftId.value = null;
    
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value);
      autoSaveTimer.value = null;
    }
    
    saveFormDrafts();
    logger.logInfo('All form drafts cleared');
  };

  /**
   * Clear the active draft (after successful submission)
   */
  const clearActiveDraft = (): void => {
    if (activeDraftId.value) {
      deleteDraft(activeDraftId.value);
      activeDraftId.value = null;
    }
  };

  /**
   * Clean up old drafts
   */
  const cleanupOldDrafts = (): void => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_INTERVAL_DAYS);
    
    const initialCount = formDrafts.value.length;
    formDrafts.value = formDrafts.value.filter(draft => {
      const draftDate = new Date(draft.timestamp);
      return draftDate > cutoffDate;
    });
    
    const removedCount = initialCount - formDrafts.value.length;
    if (removedCount > 0) {
      saveFormDrafts();
      logger.logInfo('Cleaned up old form drafts', {
        removedCount,
        remainingCount: formDrafts.value.length
      });
    }
  };

  /**
   * Get draft statistics
   */
  const getDraftStats = () => {
    return {
      total: formDrafts.value.length,
      activeDraftId: activeDraftId.value,
      oldestDraft: formDrafts.value.length > 0 
        ? formDrafts.value[formDrafts.value.length - 1].timestamp 
        : null,
      newestDraft: formDrafts.value.length > 0 
        ? formDrafts.value[0].timestamp 
        : null,
      lastAutoSave: lastAutoSaveAt.value
    };
  };

  /**
   * Check if form has unsaved changes compared to active draft
   */
  const hasUnsavedChanges = (currentFormData: ReportDetails): boolean => {
    if (!activeDraft.value) {
      // No active draft, check if form has any content
      return !!(currentFormData.title.trim() || 
               currentFormData.description.trim() || 
               currentFormData.stepsToReproduce?.trim() ||
               currentFormData.expectedBehavior?.trim() ||
               currentFormData.actualBehavior?.trim() ||
               currentFormData.additionalNotes?.trim());
    }

    const draft = activeDraft.value.formData;
    return (
      currentFormData.title !== draft.title ||
      currentFormData.description !== draft.description ||
      currentFormData.type !== draft.type ||
      currentFormData.severity !== draft.severity ||
      currentFormData.stepsToReproduce !== draft.stepsToReproduce ||
      currentFormData.expectedBehavior !== draft.expectedBehavior ||
      currentFormData.actualBehavior !== draft.actualBehavior ||
      currentFormData.additionalNotes !== draft.additionalNotes
    );
  };

  // Initialize on first use
  loadFormDrafts();

  return {
    // State
    formDrafts: readonly(formDrafts),
    hasDrafts,
    draftCount,
    activeDraft,
    activeDraftId: readonly(activeDraftId),
    lastAutoSaveAt: readonly(lastAutoSaveAt),

    // Methods
    saveDraft,
    autoSaveDraft,
    loadDraft,
    getLatestDraft,
    deleteDraft,
    clearAllDrafts,
    clearActiveDraft,
    cleanupOldDrafts,
    getDraftStats,
    hasUnsavedChanges
  };
}
